# Vite + Shiki Setup File

Put this file in your project (e.g. `src/utils/shiki-setup.js`) and import it wherever you need syntax highlighting.

```js
// src/utils/shiki-setup.js
import { setCDN, getHighlighter } from 'shiki'

// Point Shiki at your public assets directory (adjust if needed)
setCDN('/assets/shiki/')

/**
 * A promise that resolves to a configured Shiki highlighter.
 * Includes only the grammars you asked for:
 *   • javascript, typescript, typescriptreact
 *   • html, css, json, xml
 *   • markdown, yaml
 *   • scss, less
 *   • python, swift, rust, go, java, php, ruby, shell
 */
export const highlighterPromise = getHighlighter({
  themes: ['nord'],
  langs: [
    'javascript',
    'typescript',
    'typescriptreact',
    'html',
    'css',
    'json',
    'xml',
    'markdown',
    'yaml',
    'scss',
    'less',
    'python',
    'swift',
    'rust',
    'go',
    'java',
    'php',
    'ruby',
    'shell'
  ]
})

/**
 * Helper to get the highlighter in async/await style:
 *
 *   import { highlighterPromise } from './utils/shiki-setup'
 *   const highlighter = await highlighterPromise
 *   const html = highlighter.codeToHtml(yourCode, { lang: 'typescript' })
 */
```

---

## Vite Configuration Snippet

Ensure your `vite.config.js` lets Vite emit the WASM and JSON grammars as separate assets:

```js
// vite.config.js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  build: {
    // Never inline .wasm or large JSON files into JS bundles
    assetsInlineLimit: 0,
    // Raise the warning threshold so you can monitor chunk sizes
    chunkSizeWarningLimit: 600
  }
})
```

---

## Next Steps & Pro Tips

- Copy Shiki’s runtime files  
  After install, copy `node_modules/shiki/*.wasm` into your `public/assets/shiki/` so `setCDN('/assets/shiki/')` can find them.

- Theme switching  
  Export multiple themes or dynamically switch by calling `getHighlighter({ themes: ['one','two'], … })`.

- Pre-bundle for SSR  
  On server-side render, call `await highlighterPromise` once at startup to avoid repetitive WASM loads.

- Smaller payloads  
  If you never need, say, `ruby` or `php`, simply drop them from the `langs` array and rebuild.

- Alternative highlighters  
  For even leaner bundles, explore Prism.js with manual language imports.

With this setup, your CEP panel ships only the languages your users need—every chunk under 500 kB and razor-sharp performance.