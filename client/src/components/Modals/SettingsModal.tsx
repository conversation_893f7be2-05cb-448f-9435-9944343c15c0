import React, { useState, useEffect } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useHistoryStore } from '../stores/historyStore';
import { CEPSettings } from '../../utils/cepIntegration';
import { Download, Upload, Trash2, Save, X, ChevronDown, Info, HelpCircle, BarChart2, RefreshCw } from 'lucide-react';

interface SettingsData {
  theme: 'light' | 'dark' | 'auto';
  autoSave: boolean;
  showNotifications: boolean;
  maxHistoryItems: number;
  debugMode: boolean;
}

interface SettingLink {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

export const SettingsModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { sessions } = useHistoryStore();
  const [settings, setSettings] = useState<SettingsData>({
    theme: 'auto',
    autoSave: true,
    showNotifications: true,
    maxHistoryItems: 100,
    debugMode: false,
  });
  const [activeTab, setActiveTab] = useState<'settings' | 'help' | 'about' | 'analytics'>('settings');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [analyticsLoading, setAnalyticsLoading] = useState(false);

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await CEPSettings.load();
        if (savedSettings.appSettings) {
          setSettings(savedSettings.appSettings);
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadSettings();
  }, []);

  const settingLinks: SettingLink[] = [
    {
      id: 'settings',
      title: 'General Settings',
      description: 'Configure application preferences',
      icon: <Save size={16} className="text-adobe-accent" />
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'View usage statistics',
      icon: <BarChart2 size={16} className="text-adobe-accent" />
    },
    {
      id: 'help',
      title: 'Help & Support',
      description: 'Get help and answers',
      icon: <HelpCircle size={16} className="text-adobe-accent" />
    },
    {
      id: 'about',
      title: 'About',
      description: 'About SahAI Extension',
      icon: <Info size={16} className="text-adobe-accent" />
    }
  ];

  // Calculate analytics data from sessions
  const calculateAnalytics = () => {
    const now = Date.now();
    const filteredSessions = sessions.filter(session => {
      if (timeRange === 'all') return true;
      const days = parseInt(timeRange.replace('d', ''));
      return now - session.createdAt <= days * 24 * 60 * 60 * 1000;
    });

    return {
      messages: filteredSessions.reduce((sum, session) => sum + session.messages.length, 0),
      sessions: filteredSessions.length,
      tokens: filteredSessions.reduce((sum, session) => sum + (session.tokenCount || 0), 0),
      cost: filteredSessions.reduce((sum, session) => sum + (session.cost || 0), 0),
      avgLatency: filteredSessions.length > 0 
        ? filteredSessions.reduce((sum, session) => sum + (session.avgLatency || 0), 0) / filteredSessions.length
        : 0
    };
  };

  const analyticsData = calculateAnalytics();

  const refreshAnalytics = () => {
    setAnalyticsLoading(true);
    setTimeout(() => setAnalyticsLoading(false), 800); // Simulate loading
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const existingSettings = await CEPSettings.load();
      const updatedSettings = {
        ...existingSettings,
        appSettings: settings
      };
      await CEPSettings.save(updatedSettings);
      closeModal();
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              Settings
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar Navigation */}
          <div className="w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto">
            <div className="space-y-1">
              {settingLinks.map((link) => (
                <button
                  key={link.id}
                  onClick={() => setActiveTab(link.id as any)}
                  className={`w-full text-left p-3 rounded-md transition-colors ${
                    activeTab === link.id
                      ? 'bg-adobe-accent/10 text-adobe-text-primary'
                      : 'text-adobe-text-secondary hover:bg-adobe-bg-tertiary'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 rounded-md bg-adobe-bg-tertiary">
                      {link.icon}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{link.title}</div>
                      <div className="text-xs mt-1">{link.description}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Content Area */}
          <div className="w-2/3 p-6 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"></div>
              </div>
            ) : activeTab === 'settings' ? (
              <div className="space-y-6">
                {/* Theme Settings */}
                <div>
                  <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Appearance</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm text-adobe-text-secondary mb-2">Theme</label>
                      <div className="relative">
                        <select
                          value={settings.theme}
                          onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as any }))}
                          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none"
                        >
                          <option value="auto">Auto (System)</option>
                          <option value="light">Light</option>
                          <option value="dark">Dark</option>
                        </select>
                        <ChevronDown 
                          size={16} 
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none" 
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* General Settings */}
                <div>
                  <h3 className="text-sm font-medium text-adobe-text-primary mb-3">General</h3>
                  <div className="space-y-3">
                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={settings.autoSave}
                        onChange={(e) => setSettings(prev => ({ ...prev, autoSave: e.target.checked }))}
                        className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                      />
                      <span className="text-sm text-adobe-text-primary">Auto-save conversations</span>
                    </label>

                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={settings.showNotifications}
                        onChange={(e) => setSettings(prev => ({ ...prev, showNotifications: e.target.checked }))}
                        className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                      />
                      <span className="text-sm text-adobe-text-primary">Show notifications</span>
                    </label>

                    <div>
                      <label className="block text-sm text-adobe-text-secondary mb-2">
                        Max history items ({settings.maxHistoryItems})
                      </label>
                      <input
                        type="range"
                        min="10"
                        max="500"
                        step="10"
                        value={settings.maxHistoryItems}
                        onChange={(e) => setSettings(prev => ({ ...prev, maxHistoryItems: parseInt(e.target.value) }))}
                        className="w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"
                      />
                    </div>
                  </div>
                </div>

                {/* Advanced Settings */}
                <div>
                  <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Advanced</h3>
                  <div className="space-y-3">
                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={settings.debugMode}
                        onChange={(e) => setSettings(prev => ({ ...prev, debugMode: e.target.checked }))}
                        className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                      />
                      <span className="text-sm text-adobe-text-primary">Debug mode</span>
                    </label>
                  </div>
                </div>

                {/* Save Button */}
                <div className="pt-4">
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
                  >
                    <Save size={16} />
                    <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
                  </button>
                </div>
              </div>
            ) : activeTab === 'analytics' ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-adobe-text-primary">Analytics</h3>
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value as any)}
                        className="appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-1.5 pr-8 text-sm text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer"
                      >
                        <option value="7d">Last 7 days</option>
                        <option value="30d">Last 30 days</option>
                        <option value="90d">Last 90 days</option>
                        <option value="all">All time</option>
                      </select>
                      <ChevronDown 
                        size={14} 
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none" 
                      />
                    </div>
                    <button
                      onClick={refreshAnalytics}
                      disabled={analyticsLoading}
                      className="p-1.5 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
                    >
                      <RefreshCw size={16} className={analyticsLoading ? 'animate-spin' : ''} />
                    </button>
                  </div>
                </div>

                {analyticsLoading ? (
                  <div className="grid grid-cols-2 gap-4">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="h-24 bg-adobe-bg-secondary rounded-md animate-pulse"></div>
                    ))}
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-adobe-bg-secondary p-4 rounded-md">
                        <div className="text-sm text-adobe-text-secondary mb-1">Messages</div>
                        <div className="text-2xl font-medium text-adobe-text-primary">
                          {analyticsData.messages.toLocaleString()}
                        </div>
                      </div>
                      <div className="bg-adobe-bg-secondary p-4 rounded-md">
                        <div className="text-sm text-adobe-text-secondary mb-1">Sessions</div>
                        <div className="text-2xl font-medium text-adobe-text-primary">
                          {analyticsData.sessions.toLocaleString()}
                        </div>
                      </div>
                      <div className="bg-adobe-bg-secondary p-4 rounded-md">
                        <div className="text-sm text-adobe-text-secondary mb-1">Tokens Used</div>
                        <div className="text-2xl font-medium text-adobe-text-primary">
                          {analyticsData.tokens.toLocaleString()}
                        </div>
                      </div>
                      <div className="bg-adobe-bg-secondary p-4 rounded-md">
                        <div className="text-sm text-adobe-text-secondary mb-1">Est. Cost</div>
                        <div className="text-2xl font-medium text-adobe-text-primary">
                          ${analyticsData.cost.toFixed(4)}
                        </div>
                      </div>
                    </div>

                    <div className="bg-adobe-bg-secondary p-4 rounded-md">
                      <div className="flex items-center justify-between mb-2">
                        <div className="text-sm text-adobe-text-secondary">Average Latency</div>
                        <div className="text-sm font-medium text-adobe-text-primary">
                          {analyticsData.avgLatency.toFixed(2)} seconds
                        </div>
                      </div>
                      <div className="h-2 bg-adobe-bg-tertiary rounded-full overflow-hidden">
                        <div
                          className="h-full bg-adobe-accent transition-all duration-300"
                          style={{ width: `${Math.min(100, analyticsData.avgLatency * 50)}%` }}
                        />
                      </div>
                    </div>

                    <div className="bg-adobe-bg-secondary p-4 rounded-md">
                      <h4 className="text-sm font-medium text-adobe-text-primary mb-2">Performance Tips</h4>
                      <ul className="text-sm text-adobe-text-secondary space-y-1">
                        <li>• Use concise prompts to reduce token usage</li>
                        <li>• Select faster models for simple tasks</li>
                        <li>• Monitor usage to optimize costs</li>
                      </ul>
                    </div>
                  </>
                )}
              </div>
            ) : activeTab === 'help' ? (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-adobe-text-primary mb-2">Help & Support</h3>
                <div className="space-y-3">
                  <div className="p-4 bg-adobe-bg-secondary rounded-md">
                    <h4 className="font-medium text-adobe-text-primary mb-2">Documentation</h4>
                    <p className="text-sm text-adobe-text-secondary">Read our comprehensive documentation for detailed guides.</p>
                  </div>
                  <div className="p-4 bg-adobe-bg-secondary rounded-md">
                    <h4 className="font-medium text-adobe-text-primary mb-2">FAQ</h4>
                    <p className="text-sm text-adobe-text-secondary">Find answers to frequently asked questions.</p>
                  </div>
                  <div className="p-4 bg-adobe-bg-secondary rounded-md">
                    <h4 className="font-medium text-adobe-text-primary mb-2">Contact Support</h4>
                    <p className="text-sm text-adobe-text-secondary">Email <NAME_EMAIL> for assistance.</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-adobe-text-secondary">
                <div className="text-center">
                  <Info size={48} className="mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium text-adobe-text-primary mb-2">About SahAI</h3>
                  <p className="text-sm mb-4">Version 2.0.0</p>
                  <p className="text-sm">AI-powered assistant for Adobe Creative Suite</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-adobe-bg-secondary border-t border-adobe-border p-3 text-xs text-adobe-text-secondary text-center">
          {activeTab === 'settings' && 'General Settings'}
          {activeTab === 'analytics' && 'Usage Analytics'}
          {activeTab === 'help' && 'Help & Support'}
          {activeTab === 'about' && 'About SahAI'}
        </div>
      </div>
    </div>
  );
};