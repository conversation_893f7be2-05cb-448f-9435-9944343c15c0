import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { StatusModal } from './StatusModal';
import { useModalStore } from '../stores/modalStore';
import { useSettingsStore } from '../stores/settingsStore';
import { ProviderStatusChecker } from '../../utils/cepIntegration';

// Mock the stores
jest.mock('../stores/modalStore');
jest.mock('../stores/settingsStore');
jest.mock('../../utils/cepIntegration');

const mockCloseModal = jest.fn();
const mockGetActiveProvider = jest.fn();

const mockUseModalStore = useModalStore as jest.MockedFunction<typeof useModalStore>;
const mockUseSettingsStore = useSettingsStore as jest.MockedFunction<typeof useSettingsStore>;
const mockProviderStatusChecker = ProviderStatusChecker as jest.Mocked<typeof ProviderStatusChecker>;

describe('StatusModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseModalStore.mockReturnValue({
      closeModal: mockCloseModal,
      openModal: jest.fn(),
      currentModal: null,
    });

    mockUseSettingsStore.mockReturnValue({
      getActiveProvider: mockGetActiveProvider,
      providers: [],
      getActiveModel: jest.fn(),
      loadSettings: jest.fn(),
      saveSettings: jest.fn(),
      updateProvider: jest.fn(),
      deleteProvider: jest.fn(),
      addProvider: jest.fn(),
    });
  });

  it('renders correctly with no active provider', () => {
    mockGetActiveProvider.mockReturnValue(null);

    render(<StatusModal />);

    expect(screen.getByText('Provider Status')).toBeInTheDocument();
    expect(screen.getByText('No provider selected')).toBeInTheDocument();
    expect(screen.getByText('Select a provider to check connection status')).toBeInTheDocument();
  });

  it('renders correctly with active provider', async () => {
    const mockProvider = {
      id: 'test-provider',
      name: 'Test Provider',
      baseURL: 'https://api.test.com',
      apiKey: 'test-key',
      isConfigured: true,
    };

    mockGetActiveProvider.mockReturnValue(mockProvider);
    mockProviderStatusChecker.checkProviderStatus.mockResolvedValue({
      isOnline: true,
      latency: 150,
    });

    render(<StatusModal />);

    expect(screen.getByText('Provider Status')).toBeInTheDocument();
    expect(screen.getByText('Test Provider')).toBeInTheDocument();

    // Wait for status check to complete
    await waitFor(() => {
      expect(screen.getByText('Online')).toBeInTheDocument();
    });

    expect(screen.getByText('150ms')).toBeInTheDocument();
    expect(screen.getByText('Good')).toBeInTheDocument(); // Latency quality indicator
  });

  it('handles status check errors', async () => {
    const mockProvider = {
      id: 'test-provider',
      name: 'Test Provider',
      baseURL: 'https://api.test.com',
      apiKey: 'test-key',
      isConfigured: true,
    };

    mockGetActiveProvider.mockReturnValue(mockProvider);
    mockProviderStatusChecker.checkProviderStatus.mockRejectedValue(
      new Error('Connection failed')
    );

    render(<StatusModal />);

    await waitFor(() => {
      expect(screen.getByText('Offline')).toBeInTheDocument();
    });

    expect(screen.getByText('Error:')).toBeInTheDocument();
    expect(screen.getByText('Connection failed')).toBeInTheDocument();
  });

  it('closes modal when close button is clicked', () => {
    mockGetActiveProvider.mockReturnValue(null);

    render(<StatusModal />);

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockCloseModal).toHaveBeenCalledTimes(1);
  });

  it('refreshes status when refresh button is clicked', async () => {
    const mockProvider = {
      id: 'test-provider',
      name: 'Test Provider',
      baseURL: 'https://api.test.com',
      apiKey: 'test-key',
      isConfigured: true,
    };

    mockGetActiveProvider.mockReturnValue(mockProvider);
    mockProviderStatusChecker.checkProviderStatus.mockResolvedValue({
      isOnline: true,
      latency: 100,
    });

    render(<StatusModal />);

    const refreshButton = screen.getByTitle('Refresh status');
    fireEvent.click(refreshButton);

    expect(mockProviderStatusChecker.checkProviderStatus).toHaveBeenCalledWith(
      'test-provider',
      { apiKey: 'test-key', baseURL: 'https://api.test.com' }
    );
  });

  it('copies endpoint URL when copy button is clicked', async () => {
    const mockProvider = {
      id: 'test-provider',
      name: 'Test Provider',
      baseURL: 'https://api.test.com',
      apiKey: 'test-key',
      isConfigured: true,
    };

    mockGetActiveProvider.mockReturnValue(mockProvider);
    
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn(),
      },
    });

    render(<StatusModal />);

    const copyButton = screen.getByText('Copy');
    fireEvent.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('https://api.test.com');
  });
});
