import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { TopBar } from './TopBar';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { useChatStore } from '../stores/chatStore';

// Mock the stores
jest.mock('../stores/settingsStore');
jest.mock('../stores/modalStore');
jest.mock('../stores/chatStore');
jest.mock('../ui/ProviderStatusIndicator', () => ({
  ProviderStatusIndicator: () => <div data-testid="status-indicator">Status</div>
}));

const mockUseSettingsStore = useSettingsStore as jest.MockedFunction<typeof useSettingsStore>;
const mockUseModalStore = useModalStore as jest.MockedFunction<typeof useModalStore>;
const mockUseChatStore = useChatStore as jest.MockedFunction<typeof useChatStore>;

describe('TopBar', () => {
  const mockOpenModal = jest.fn();
  const mockCreateNewSession = jest.fn();
  const mockLoadSettings = jest.fn();
  const mockGetActiveProvider = jest.fn();
  const mockGetActiveModel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseSettingsStore.mockReturnValue({
      getActiveProvider: mockGetActiveProvider,
      getActiveModel: mockGetActiveModel,
      loadSettings: mockLoadSettings,
    } as any);

    mockUseModalStore.mockReturnValue({
      openModal: mockOpenModal,
    } as any);

    mockUseChatStore.mockReturnValue({
      createNewSession: mockCreateNewSession,
    } as any);
  });

  it('renders all TopBar elements correctly', () => {
    mockGetActiveProvider.mockReturnValue(null);
    mockGetActiveModel.mockReturnValue(null);

    render(<TopBar />);

    // Check if provider selector is rendered
    expect(screen.getByText('Select AI Provider & Model')).toBeInTheDocument();
    
    // Check if all buttons are rendered
    expect(screen.getByTitle('New Chat')).toBeInTheDocument();
    expect(screen.getByTitle('Chat History')).toBeInTheDocument();
    expect(screen.getByTitle('Settings')).toBeInTheDocument();
    expect(screen.getByTitle('Provider Status')).toBeInTheDocument();
    
    // Check if status indicator is rendered
    expect(screen.getByTestId('status-indicator')).toBeInTheDocument();
  });

  it('displays provider and model when selected', () => {
    const mockProvider = { id: 'openai', name: 'OpenAI' };
    const mockModel = { id: 'gpt-4', name: 'GPT-4' };
    
    mockGetActiveProvider.mockReturnValue(mockProvider);
    mockGetActiveModel.mockReturnValue(mockModel);

    render(<TopBar />);

    expect(screen.getByText('OpenAI • GPT-4')).toBeInTheDocument();
  });

  it('displays provider without model when model not selected', () => {
    const mockProvider = { id: 'openai', name: 'OpenAI' };
    
    mockGetActiveProvider.mockReturnValue(mockProvider);
    mockGetActiveModel.mockReturnValue(null);

    render(<TopBar />);

    expect(screen.getByText('OpenAI • Select Model')).toBeInTheDocument();
  });

  it('opens provider modal when provider selector is clicked', () => {
    mockGetActiveProvider.mockReturnValue(null);
    mockGetActiveModel.mockReturnValue(null);

    render(<TopBar />);

    fireEvent.click(screen.getByTitle('Select AI Provider & Model'));
    expect(mockOpenModal).toHaveBeenCalledWith('provider');
  });

  it('opens correct modals when buttons are clicked', () => {
    mockGetActiveProvider.mockReturnValue(null);
    mockGetActiveModel.mockReturnValue(null);

    render(<TopBar />);

    // Test chat history modal
    fireEvent.click(screen.getByTitle('Chat History'));
    expect(mockOpenModal).toHaveBeenCalledWith('chat-history');

    // Test settings modal
    fireEvent.click(screen.getByTitle('Settings'));
    expect(mockOpenModal).toHaveBeenCalledWith('settings');

    // Test status modal
    fireEvent.click(screen.getByTitle('Provider Status'));
    expect(mockOpenModal).toHaveBeenCalledWith('status');
  });

  it('creates new session when new chat button is clicked', () => {
    mockGetActiveProvider.mockReturnValue(null);
    mockGetActiveModel.mockReturnValue(null);

    render(<TopBar />);

    fireEvent.click(screen.getByTitle('New Chat'));
    expect(mockCreateNewSession).toHaveBeenCalled();
  });

  it('loads settings on mount', () => {
    mockGetActiveProvider.mockReturnValue(null);
    mockGetActiveModel.mockReturnValue(null);

    render(<TopBar />);

    expect(mockLoadSettings).toHaveBeenCalled();
  });
});
