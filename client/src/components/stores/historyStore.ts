import { create } from 'zustand';
import { executeExtendScript } from '../../utils/cepIntegration';

export interface ChatSession {
  id: string;
  title: string;
  messages: Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
  createdAt: number;
  updatedAt: number;
  provider?: string;
  model?: string;
  tokenCount?: number;
  cost?: number;
  avgLatency?: number;
}

interface HistoryState {
  sessions: ChatSession[];
  currentSessionId: string | null;
  isLoading: boolean;
  error: string | null;

  // Session management
  loadHistory: () => Promise<void>;
  saveSession: (session: ChatSession) => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  clearHistory: () => Promise<void>;
  
  // Session operations
  createSession: (title?: string) => ChatSession;
  updateSession: (sessionId: string, updates: Partial<ChatSession>) => void;
  setCurrentSession: (sessionId: string | null) => void;
  
  // Computed getters
  getCurrentSession: () => ChatSession | null;
  getSessionById: (sessionId: string) => ChatSession | null;
  getSortedSessions: () => ChatSession[];
}

export const useHistoryStore = create<HistoryState>((set, get) => ({
  sessions: [],
  currentSessionId: null,
  isLoading: false,
  error: null,

  loadHistory: async () => {
    set({ isLoading: true, error: null });
    try {
      const result = await executeExtendScript('loadHistory()');
      if (result.success && result.data) {
        const sessions = Array.isArray(result.data) ? result.data : [];
        set({ sessions, isLoading: false });
      } else {
        set({ sessions: [], isLoading: false });
      }
    } catch (error: any) {
      console.error('Failed to load history:', error);
      set({ 
        error: error.message || 'Failed to load chat history',
        isLoading: false,
        sessions: [] 
      });
    }
  },

  saveSession: async (session: ChatSession) => {
    try {
      // Update local state first
      set(state => ({
        sessions: state.sessions.some(s => s.id === session.id)
          ? state.sessions.map(s => s.id === session.id ? session : s)
          : [...state.sessions, session]
      }));

      // Save to ExtendScript
      const allSessions = get().sessions;
      await executeExtendScript(`saveHistory(${JSON.stringify(allSessions)})`);
    } catch (error: any) {
      console.error('Failed to save session:', error);
      set({ error: error.message || 'Failed to save session' });
    }
  },

  deleteSession: async (sessionId: string) => {
    try {
      // Update local state
      set(state => ({
        sessions: state.sessions.filter(s => s.id !== sessionId),
        currentSessionId: state.currentSessionId === sessionId ? null : state.currentSessionId
      }));

      // Save updated sessions to ExtendScript
      const allSessions = get().sessions;
      await executeExtendScript(`saveHistory(${JSON.stringify(allSessions)})`);
    } catch (error: any) {
      console.error('Failed to delete session:', error);
      set({ error: error.message || 'Failed to delete session' });
    }
  },

  clearHistory: async () => {
    try {
      set({ sessions: [], currentSessionId: null });
      await executeExtendScript('saveHistory([])');
    } catch (error: any) {
      console.error('Failed to clear history:', error);
      set({ error: error.message || 'Failed to clear history' });
    }
  },

  createSession: (title?: string) => {
    const session: ChatSession = {
      id: crypto.randomUUID(),
      title: title || `Chat ${new Date().toLocaleDateString()}`,
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };
    
    set(state => ({
      sessions: [session, ...state.sessions],
      currentSessionId: session.id
    }));
    
    return session;
  },

  updateSession: (sessionId: string, updates: Partial<ChatSession>) => {
    set(state => ({
      sessions: state.sessions.map(session =>
        session.id === sessionId
          ? { ...session, ...updates, updatedAt: Date.now() }
          : session
      )
    }));
  },

  setCurrentSession: (sessionId: string | null) => {
    set({ currentSessionId: sessionId });
  },

  getCurrentSession: () => {
    const { sessions, currentSessionId } = get();
    return sessions.find(s => s.id === currentSessionId) || null;
  },

  getSessionById: (sessionId: string) => {
    const { sessions } = get();
    return sessions.find(s => s.id === sessionId) || null;
  },

  getSortedSessions: () => {
    const { sessions } = get();
    return [...sessions].sort((a, b) => b.updatedAt - a.updatedAt);
  },
}));
