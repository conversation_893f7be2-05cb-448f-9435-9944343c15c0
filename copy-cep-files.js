#!/usr/bin/env node
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT = path.resolve(__dirname);
const DIST = path.join(ROOT, 'dist');

// Files/folders that **Vite does NOT bundle** but CEP needs
const CEP_ASSETS = [
  'CSXS',               // manifest.xml
  'host',               // ae-integration.jsx
  'icons',              // icon-16.png / icon-32.png
  '.debug',             // enables debug mode
];

// Individual files to copy to dist root
const CEP_FILES = [
  'client/CSInterface.js', // CEP JavaScript API
];

// Additional assets to copy (like Shiki WASM files)
const ADDITIONAL_ASSETS = [
  {
    src: 'client/public/assets',
    dest: 'assets',
    description: 'Shiki WASM files and other public assets'
  }
];

async function copyDir(src, dest) {
  await fs.promises.mkdir(dest, { recursive: true });
  const entries = await fs.promises.readdir(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDir(srcPath, destPath);
    } else {
      await fs.promises.copyFile(srcPath, destPath);
    }
  }
}

async function main() {
  console.log('🚀 Copying CEP assets to dist/');
  await fs.promises.mkdir(DIST, { recursive: true });

  for (const item of CEP_ASSETS) {
    const src = path.join(ROOT, item);
    const dest = path.join(DIST, item);
    if (fs.existsSync(src)) {
      const stat = await fs.promises.stat(src);
      if (stat.isDirectory()) {
        await copyDir(src, dest);
      } else {
        await fs.promises.copyFile(src, dest);
      }
      console.log(`  ✅ copied ${item}`);
    } else {
      console.log(`  ⚠️  ${item} not found – skipped`);
    }
  }

  // Optional: remove .DS_Store / Thumbs.db files
  try {
    await fs.promises.unlink(path.join(DIST, '.DS_Store'));
  } catch (e) { /* ignore */ }
  try {
    await fs.promises.unlink(path.join(DIST, 'Thumbs.db'));
  } catch (e) { /* ignore */ }

  // Copy individual files to dist root
  for (const file of CEP_FILES) {
    const src = path.join(ROOT, file);
    const dest = path.join(DIST, path.basename(file));
    if (fs.existsSync(src)) {
      await fs.promises.copyFile(src, dest);
      console.log(`  ✅ copied ${file} to dist root`);
    } else {
      console.log(`  ⚠️  ${file} not found – skipped`);
    }
  }

  // Copy additional assets (like Shiki WASM files)
  for (const asset of ADDITIONAL_ASSETS) {
    const src = path.join(ROOT, asset.src);
    const dest = path.join(DIST, asset.dest);
    if (fs.existsSync(src)) {
      await copyDir(src, dest);
      console.log(`  ✅ copied ${asset.description}`);
    } else {
      console.log(`  ⚠️  ${asset.src} not found – skipped`);
    }
  }

  console.log('🎉 dist/ ready for CEP');
}

main().catch(console.error);
