var me=Object.defineProperty;var xe=(o,r,a)=>r in o?me(o,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[r]=a;var V=(o,r,a)=>xe(o,typeof r!="symbol"?r+"":r,a);import{r as c,a as ue,R as X}from"./assets/react-vendor-CIP6LD3P.js";import{_ as Z,c as he}from"./assets/shiki-DBOBms81.js";import{c as R,C as P,P as be,H as pe,S as ge,a as fe,D as ye,A as ve,b as je,M as Ne,L as I,d as Se,e as H,X as O,f as q,R as ee,I as J,B as we,g as Ce,h as Y,T as Le,i as ke,j as W,W as Me,k as Ee}from"./assets/ui-vendor-DXj3V-Mi.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const n of s)if(n.type==="childList")for(const d of n.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&t(d)}).observe(document,{childList:!0,subtree:!0});function a(s){const n={};return s.integrity&&(n.integrity=s.integrity),s.referrerPolicy&&(n.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?n.credentials="include":s.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function t(s){if(s.ep)return;s.ep=!0;const n=a(s);fetch(s.href,n)}})();var te={exports:{}},T={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ae=c,ze=Symbol.for("react.element"),Pe=Symbol.for("react.fragment"),Ie=Object.prototype.hasOwnProperty,Oe=Ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Re={key:!0,ref:!0,__self:!0,__source:!0};function se(o,r,a){var t,s={},n=null,d=null;a!==void 0&&(n=""+a),r.key!==void 0&&(n=""+r.key),r.ref!==void 0&&(d=r.ref);for(t in r)Ie.call(r,t)&&!Re.hasOwnProperty(t)&&(s[t]=r[t]);if(o&&o.defaultProps)for(t in r=o.defaultProps,r)s[t]===void 0&&(s[t]=r[t]);return{$$typeof:ze,type:o,key:n,ref:d,props:s,_owner:Oe.current}}T.Fragment=Pe;T.jsx=se;T.jsxs=se;te.exports=T;var e=te.exports,B={},Q=ue;B.createRoot=Q.createRoot,B.hydrateRoot=Q.hydrateRoot;let _=null;const E=()=>typeof window<"u"&&!!window.CSInterface,G=()=>{if(!_&&E())try{_=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(o){console.error("Failed to initialize CSInterface:",o)}return _},ae=()=>{if(!E()){console.warn("Not running in CEP environment");return}const o=G();if(!o)return;o.addEventListener("com.adobe.csxs.events.ThemeColorChanged",a=>{console.log("Theme changed:",a)});const r=o.getHostEnvironment();console.log("Host environment:",r),o.evalScript("SahAI.getAppInfo()",a=>{try{if(!a||a.trim()===""){console.warn("Empty response from ExtendScript");return}const t=JSON.parse(a);console.log("ExtendScript response:",t)}catch(t){console.error("Failed to parse ExtendScript response:",t,"Raw result:",a)}})},k=(o,r=3e4)=>new Promise((a,t)=>{const s=G();if(!s){t(new Error("CSInterface not available - not running in CEP environment"));return}const n=setTimeout(()=>{t(new Error(`ExtendScript execution timed out after ${r}ms`))},r);try{s.evalScript(o,d=>{clearTimeout(n);try{if(typeof d=="string"&&d.startsWith("EvalScript error")){t(new Error(`ExtendScript Error: ${d}`));return}if(!d||d.trim()===""){t(new Error("Empty response from ExtendScript"));return}let l;try{l=JSON.parse(d)}catch{l={success:!0,data:d}}typeof l=="object"&&l!==null?l.success===!1?t(new Error(l.message||"ExtendScript execution failed")):a(l):a({success:!0,data:l})}catch(l){t(new Error(`Failed to process ExtendScript response: ${l}`))}})}catch(d){clearTimeout(n),t(new Error(`Failed to execute ExtendScript: ${d}`))}});class M{static async save(r){const a=JSON.stringify(r);try{if(E())try{const t=await k(`saveSettings(${JSON.stringify(r)})`,1e4);if(t.success)console.log("Settings saved to CEP storage successfully");else throw new Error(t.message||"CEP save failed")}catch(t){console.warn("CEP storage save failed, falling back to localStorage:",t)}localStorage.setItem(this.SETTINGS_KEY,a),console.log("Settings saved to localStorage successfully")}catch(t){console.error("All settings save methods failed:",t);try{localStorage.setItem(this.SETTINGS_KEY,a)}catch(s){throw new Error(`Failed to save settings: ${t}. LocalStorage also failed: ${s}`)}}}static async load(){try{if(E())try{const a=await k("loadSettings()",1e4);if(a.success&&a.data)return console.log("Settings loaded from CEP storage successfully"),a.data}catch(a){console.warn("CEP storage load failed, falling back to localStorage:",a)}const r=localStorage.getItem(this.SETTINGS_KEY);if(r){const a=JSON.parse(r);return console.log("Settings loaded from localStorage successfully"),a}return console.log("No existing settings found, returning defaults"),{providers:[]}}catch(r){return console.error("All settings load methods failed:",r),{providers:[]}}}static async exportSettings(){const r=await this.load();return JSON.stringify(r,null,2)}static async importSettings(r){try{const a=JSON.parse(r);await this.save(a)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(E())try{await k("saveSettings({})",1e4)}catch(r){console.warn("Failed to clear CEP storage:",r)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(r){throw new Error(`Failed to clear settings: ${r}`)}}}V(M,"SETTINGS_KEY","sahAI_settings");class U{static async checkProviderStatus(r,a){const t=Date.now();try{const{ProviderBridge:s}=await Z(async()=>{const{ProviderBridge:d}=await Promise.resolve().then(()=>re);return{ProviderBridge:d}},void 0,import.meta.url);return{isOnline:(await s.listModels(r,a.baseURL,a.apiKey)).length>0,latency:Date.now()-t}}catch(s){return{isOnline:!1,error:s.message||String(s),latency:Date.now()-t}}}}const Te={async listModels(o,r,a){try{const t=`listModels('${o}', '${r||""}', '${a||""}')`,s=await k(t,15e3);if(s&&typeof s=="object"){if(s.success&&s.data){const n=typeof s.data=="string"?JSON.parse(s.data):s.data;return n.ok?n.models:this.getFallbackModels(o)}if(s.ok)return s.models||[]}return console.warn(`Failed to fetch models for ${o}, using fallback`),this.getFallbackModels(o)}catch(t){return console.error(`Error fetching models for ${o}:`,t),this.getFallbackModels(o)}},getFallbackModels(o){return{openai:[{id:"gpt-4o",name:"GPT-4o",description:"Most capable OpenAI model",contextLength:128e3,isRecommended:!0},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster, more affordable",contextLength:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Legacy model",contextLength:16384}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Anthropic's most capable model",contextLength:2e5,isRecommended:!0},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fast and efficient",contextLength:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Powerful reasoning",contextLength:2e5}],gemini:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6}],groq:[{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Fast inference",contextLength:131072},{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Balanced performance",contextLength:131072,isRecommended:!0},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Large context",contextLength:32768}],deepseek:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose",contextLength:128e3,isRecommended:!0},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Code-focused",contextLength:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",description:"Most capable",contextLength:128e3,isRecommended:!0},{id:"mistral-medium-latest",name:"Mistral Medium",description:"Balanced",contextLength:32e3},{id:"mistral-small-latest",name:"Mistral Small",description:"Fast and efficient",contextLength:32e3}],ollama:[{id:"llama2",name:"Llama 2",description:"Open source LLM",contextLength:4096},{id:"mistral",name:"Mistral",description:"Efficient transformer",contextLength:8192},{id:"codellama",name:"Code Llama",description:"Code-focused",contextLength:16384}],lmstudio:[{id:"local-model",name:"Local Model",description:"Your local model",contextLength:4096}]}[o]||[]}},re=Object.freeze(Object.defineProperty({__proto__:null,CEPSettings:M,ProviderBridge:Te,ProviderStatusChecker:U,executeExtendScript:k,getCSInterface:G,initializeCEP:ae,isCEPEnvironment:E},Symbol.toStringTag,{value:"Module"})),z=R((o,r)=>({providers:[{id:"openai",name:"OpenAI",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",isConfigured:!1,models:[]},{id:"groq",name:"Groq",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",isConfigured:!1,models:[]},{id:"mistral",name:"Mistral",isConfigured:!1,models:[]},{id:"moonshot",name:"Moonshot AI",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",isConfigured:!1,models:[]},{id:"perplexity",name:"Perplexity",isConfigured:!1,models:[]},{id:"qwen",name:"Alibaba Qwen",isConfigured:!1,models:[]},{id:"together",name:"Together AI",isConfigured:!1,models:[]},{id:"vertex",name:"Google Vertex AI",isConfigured:!1,models:[]},{id:"xai",name:"xAI",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",isConfigured:!1,models:[]},{id:"lmstudio",name:"LM Studio",isConfigured:!1,models:[]}],activeProviderId:void 0,isLoadingModels:!1,setActiveProvider:a=>{o({activeProviderId:a}),r().persistSettings()},updateProviderConfig:(a,t)=>{o(s=>({providers:s.providers.map(n=>n.id===a?{...n,...t,isConfigured:!!t.apiKey}:n)})),r().persistSettings()},setProviderModels:(a,t)=>{o(s=>({providers:s.providers.map(n=>n.id===a?{...n,models:t,isLoading:!1,error:void 0}:n)}))},setSelectedModel:(a,t)=>{o(s=>({providers:s.providers.map(n=>n.id===a?{...n,selectedModelId:t}:n)})),r().persistSettings()},updateProviderKey:(a,t,s)=>{o(n=>({providers:n.providers.map(d=>d.id===a?{...d,apiKey:t,isConfigured:!!t,selectedModelId:s||d.selectedModelId}:d)})),r().persistSettings()},saveProviderSelection:(a,t)=>{o(s=>({activeProviderId:a,providers:s.providers.map(n=>n.id===a?{...n,...t,isConfigured:!!(t.apiKey||n.baseURL)}:n)})),r().persistSettings()},loadModelsForProvider:async a=>{const t=r().providers.find(s=>s.id===a);if(t!=null&&t.isConfigured){o(s=>({providers:s.providers.map(n=>n.id===a?{...n,isLoading:!0,error:void 0}:n)}));try{const{ProviderBridge:s}=await Z(async()=>{const{ProviderBridge:l}=await Promise.resolve().then(()=>re);return{ProviderBridge:l}},void 0,import.meta.url),d=(await s.listModels(a,t.baseURL,t.apiKey)).map(l=>({id:l.id,name:l.name,description:l.description,contextLength:l.contextLength,isRecommended:l.isRecommended}));r().setProviderModels(a,d)}catch(s){o(n=>({providers:n.providers.map(d=>d.id===a?{...d,isLoading:!1,error:s.message||String(s)}:d)}))}}},persistSettings:()=>{const{activeProviderId:a,providers:t}=r();M.save({activeProviderId:a,providers:t.map(s=>({id:s.id,isConfigured:s.isConfigured,apiKey:s.apiKey,baseURL:s.baseURL,selectedModelId:s.selectedModelId,settings:s.settings}))})},loadSettings:async()=>{try{const a=await M.load();a.activeProviderId&&o({activeProviderId:a.activeProviderId}),a.providers&&Array.isArray(a.providers)&&o(t=>({providers:t.providers.map(s=>{var d;const n=(d=a.providers)==null?void 0:d.find(l=>l.id===s.id);return n?{...s,...n}:s})}))}catch(a){console.error("Failed to load CEP settings:",a)}},getActiveProvider:()=>{const{providers:a,activeProviderId:t}=r();return a.find(s=>s.id===t)||null},getActiveModel:()=>{const a=r().getActiveProvider();return a!=null&&a.selectedModelId&&a.models.find(t=>t.id===a.selectedModelId)||null}})),A=R(o=>({modal:null,openModal:r=>o({modal:r}),closeModal:()=>o({modal:null})})),K=R(o=>({messages:[],isLoading:!1,addMessage:r=>o(a=>({messages:[...a.messages,{...r,id:crypto.randomUUID(),timestamp:Date.now()}]})),setLoading:r=>o({isLoading:r}),createNewSession:()=>o({messages:[],currentSession:crypto.randomUUID()})})),De=()=>{const{getActiveProvider:o}=z(),[r,a]=c.useState({isOnline:null,isChecking:!1}),t=o();c.useEffect(()=>{let d;const l=async()=>{if(!(t!=null&&t.isConfigured)){a({isOnline:null,isChecking:!1});return}a(m=>({...m,isChecking:!0,error:void 0}));try{const m=await U.checkProviderStatus(t.id,{apiKey:t.apiKey,baseURL:t.baseURL});a({isOnline:m.isOnline,latency:m.latency,isChecking:!1})}catch(m){a({isOnline:!1,isChecking:!1,error:m.message})}};return t!=null&&t.isConfigured&&(l(),d=setInterval(l,3e4)),()=>{d&&clearInterval(d)}},[t]);const s=()=>{const d="w-2.5 h-2.5 rounded-full transition-all duration-300 shadow-sm hover:scale-110 cursor-help";return r.isChecking?`${d} bg-adobe-warning animate-pulse shadow-adobe-warning/40`:r.isOnline===!0?`${d} bg-adobe-success shadow-adobe-success/40`:`${d} bg-adobe-error shadow-adobe-error/40`},n=()=>t?r.isChecking?"Checking connection…":r.isOnline===!0?`${t.name} online${r.latency?` (${r.latency} ms)`:""}`:r.error?`${t.name} error: ${r.error}`:`${t.name} offline`:"No provider selected";return e.jsx("div",{className:s(),title:n()})},_e=()=>{const{getActiveProvider:o,getActiveModel:r,loadSettings:a}=z(),{openModal:t}=A(),{createNewSession:s}=K(),n=o(),d=r(),l=c.useMemo(()=>n?d?`${n.name} • ${d.name}`:`${n.name} • Select Model`:"Select AI Provider & Model",[n,d]);return c.useEffect(()=>{a()},[a]),e.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>t("status"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all flex items-center justify-center",title:"Provider Status",children:e.jsx(De,{})}),e.jsxs("button",{onClick:()=>t("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[e.jsx("span",{className:"max-w-[300px] truncate",children:l}),e.jsx(P,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:s,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:e.jsx(be,{size:16})}),e.jsx("div",{className:"h-4 w-px bg-adobe-border"}),e.jsx("button",{onClick:()=>t("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:e.jsx(pe,{size:16})}),e.jsx("button",{onClick:()=>t("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:e.jsx(ge,{size:16})})]})]})},Fe=["javascript","typescript","jsx","tsx","html","css","scss","less","json","jsonc","xml","yaml","markdown","python","swift","rust","go","java","php","ruby","shell","actionscript-3"],$e=["github-dark"],He=he({themes:$e,langs:Fe});let F=null;async function Be(){return F||(F=await He),F}function oe(o){return["javascript","typescript","jsx","tsx","html","css","scss","less","json","jsonc","xml","yaml","markdown","python","swift","rust","go","java","php","ruby","shell","actionscript-3","actionscript"].includes(o.toLowerCase())}function Ge(o){const r={js:"javascript",ts:"typescript",bash:"shell",sh:"shell",zsh:"shell",fish:"shell",py:"python",rb:"ruby",yml:"yaml",htm:"html",sass:"scss",jsx:"jsx",tsx:"tsx"},a=o.toLowerCase();return r[a]?r[a]:oe(a)?a:"text"}const Ue=({content:o})=>{const[r,a]=c.useState(null);if(c.useEffect(()=>{Be().then(a)},[]),!r)return e.jsx("pre",{className:"whitespace-pre-wrap",children:o});const t=o.split(/(```[\s\S]*?```)/g);return e.jsx(e.Fragment,{children:t.map((s,n)=>{if(s.startsWith("```")){const d=s.split(`
`),l=d[0].replace("```","").trim(),m=d.slice(1,-1).join(`
`),b=oe(l)?l:Ge(l);return e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"absolute top-1 right-1 flex gap-1",children:[e.jsx("button",{title:"Copy",onClick:()=>navigator.clipboard.writeText(m),className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:e.jsx(fe,{size:14})}),e.jsx("button",{title:"Save",className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:e.jsx(ye,{size:14})})]}),e.jsx("div",{dangerouslySetInnerHTML:{__html:r.codeToHtml(m,{lang:b,theme:"github-dark"})}})]},n)}return e.jsx("div",{children:s},n)})})},Ke=({message:o})=>{const r=o.role==="user";return e.jsx("div",{className:`flex gap-3 ${r?"justify-end":"justify-start"} mb-4`,children:e.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${r?"bg-adobe-bg-tertiary text-adobe-text-primary ml-8 rounded-br-md":"bg-adobe-bg-primary text-adobe-text-primary mr-8 rounded-bl-md border border-adobe-border"}`,children:e.jsx("div",{className:"whitespace-pre-wrap",children:e.jsx(Ue,{content:o.content})})})})},Ve=""+new URL("assets/BrandLogo-BoAYSo97.svg",import.meta.url).href,qe=()=>{const{messages:o,isLoading:r,currentSession:a}=K(),t=c.useRef(null),s=c.useRef(null),[n,d]=c.useState(!1),l=c.useRef();c.useEffect(()=>{var b;(b=t.current)==null||b.scrollIntoView({behavior:"smooth"})},[o,r]),c.useEffect(()=>{const b=s.current;if(!b)return;const f=()=>{clearTimeout(l.current);const{scrollTop:g,scrollHeight:y,clientHeight:j}=b,w=y-(g+j)<100;d(!w),l.current=setTimeout(()=>{d(!1)},2e3)};return b.addEventListener("scroll",f),()=>{b.removeEventListener("scroll",f),clearTimeout(l.current)}},[]);const m=()=>{var b;(b=t.current)==null||b.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{ref:s,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!a||o.length===0)&&e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[e.jsx("div",{className:"w-20 h-20 mb-2 flex items-center justify-center",children:e.jsx("img",{src:Ve,alt:"SahAI Logo",className:"w-20 h-20 brightness-0 invert"})}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),e.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),o.map(b=>e.jsx(Ke,{message:b},b.id)),r&&e.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:e.jsx("span",{children:"AI is thinking..."})}),e.jsx("div",{ref:t}),n&&e.jsx("button",{onClick:m,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:e.jsx(ve,{size:18})})]})},Je=X.memo(({onAttachFile:o,onVoiceInput:r})=>{const[a,t]=c.useState(""),[s,n]=c.useState(!1),d=c.useRef(null),{addMessage:l,isLoading:m,setLoading:b,currentSession:f,createNewSession:g}=K(),y=4e3,j=!a.trim(),w=a.length>y*.9;c.useEffect(()=>{const i=d.current;i&&i.style.setProperty("--textarea-height","72px")},[]);const L=c.useCallback(()=>{const i=d.current;if(!i)return;i.style.height="auto";const x=Math.min(Math.max(i.scrollHeight,72),200);i.style.setProperty("--textarea-height",`${x}px`),i.style.height=""},[]),N=c.useCallback(i=>{t(i.target.value),L()},[L]),S=c.useCallback(async()=>{const i=a.trim();if(!(!i||m)){t(""),d.current&&d.current.style.setProperty("--textarea-height","72px");try{b(!0),f||g(),l({content:i,role:"user"}),setTimeout(()=>{l({content:`Echo: ${i}`,role:"assistant"}),b(!1)},1e3)}catch{t(i),b(!1)}}},[a,m,f,l,b,g]),h=c.useCallback(i=>{i.key==="Enter"&&!i.shiftKey&&!s&&(i.preventDefault(),S())},[S,s]);return e.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[e.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[e.jsx("div",{className:"flex items-center pl-3",children:e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:m,children:e.jsx(je,{size:18})})}),e.jsx("textarea",{ref:d,rows:3,maxLength:y,value:a,onChange:N,onKeyDown:h,onCompositionStart:()=>n(!0),onCompositionEnd:()=>n(!1),placeholder:"Type a message...",className:`flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80
            auto-resize-textarea leading-relaxed overflow-y-auto chat-messages-scrollbar`}),e.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:m,children:e.jsx(Ne,{size:18})}),e.jsx("button",{onClick:S,disabled:j||m,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:m?e.jsx(I,{size:18,className:"animate-spin"}):e.jsx(Se,{size:18})})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[e.jsxs("span",{className:`text-xs ${w?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[a.length,"/",y]}),e.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),Ye=({provider:o,size:r=16,className:a="",...t})=>{const s={width:r,height:r,viewBox:"0 0 24 24",fill:"currentColor",className:`provider-logo ${a}`,...t};switch(o){case"openai":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zm-2.4569-16.2971a4.4755 4.4755 0 0 1 2.3445-1.9275L5.943 7.1778a.7663.7663 0 0 0 .3717.6388l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0L4.2446 9.8211a4.504 4.504 0 0 1-.7876-8.4285zm16.5618 3.8558l-5.8428-3.3685V4.4444a.0804.0804 0 0 1 .0332-.0615l4.8645-2.8077a4.4992 4.4992 0 0 1 6.6802 4.66l-.1465.0804-4.7806 2.7582a.7712.7712 0 0 0-.7806 0zm2.0107-3.0231l-.142.0852-4.7806 2.7582a.7663.7663 0 0 0-.3717.6388L9.74 4.1818l2.0201-1.1686a.0757.0757 0 0 1 .071 0l4.8076 2.7748a4.504 4.504 0 0 1 .7876 8.4285z"})});case"anthropic":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 22h4l2-5h8l2 5h4L12 2zm0 6l2.5 6h-5L12 8z"})});case"gemini":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"groq":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})});case"deepseek":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l10 18H2L12 2zm0 3.5L5.5 18h13L12 5.5z"})});case"mistral":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"moonshot":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3-3-8z"})});case"openrouter":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 12l10 10 10-10L12 2zm0 3.41L18.59 12 12 18.59 5.41 12 12 5.41z"})});case"perplexity":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6H9l3-3 3 3h-2v6z"})});case"qwen":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"together":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm-6 0c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2z"})});case"vertex":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"xai":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M18.36 5.64L12 12l6.36 6.36-1.41 1.41L12 14.83l-4.95 4.94-1.41-1.41L12 12 5.64 5.64l1.41-1.41L12 9.17l4.95-4.94 1.41 1.41z"})});case"ollama":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"})});case"lmstudio":return e.jsx("svg",{...s,children:e.jsx("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})});default:return e.jsx("svg",{...s,children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}},$=[{value:"openai",label:"OpenAI"},{value:"anthropic",label:"Anthropic"},{value:"gemini",label:"Google Gemini"},{value:"groq",label:"Groq"},{value:"deepseek",label:"DeepSeek"},{value:"mistral",label:"Mistral"},{value:"moonshot",label:"Moonshot AI"},{value:"openrouter",label:"OpenRouter"},{value:"perplexity",label:"Perplexity"},{value:"qwen",label:"Alibaba Qwen"},{value:"together",label:"Together AI"},{value:"vertex",label:"Google Vertex AI"},{value:"xai",label:"xAI"},{value:"ollama",label:"Ollama"},{value:"lmstudio",label:"LM Studio"}],We=()=>{const{closeModal:o}=A(),{providers:r,saveProviderSelection:a,loadModelsForProvider:t}=z(),[s,n]=c.useState(""),[d,l]=c.useState(""),[m,b]=c.useState(""),[f,g]=c.useState(""),[y,j]=c.useState(""),[w,L]=c.useState(!1),[N,S]=c.useState(!1),h=c.useRef(null),i=c.useRef(null),x=$.filter(u=>u.label.toLowerCase().includes(f.toLowerCase())),p=r.find(u=>u.id===s),v=(p==null?void 0:p.models)||[],ie=v.filter(u=>u.name.toLowerCase().includes(y.toLowerCase()));$.find(u=>u.value===s),c.useEffect(()=>{s&&m&&r.find(C=>C.id===s)&&(a(s,{apiKey:m}),t(s))},[s,m]);const de=u=>{var C;n(u),l(""),j(""),g(((C=$.find(D=>D.value===u))==null?void 0:C.label)||""),L(!1),m&&t(u)},le=u=>{l(u);const C=v.find(D=>D.id===u);j((C==null?void 0:C.name)||""),S(!1)},ce=()=>{s&&d&&m&&(a(s,{apiKey:m,selectedModelId:d}),o())};return c.useEffect(()=>{const u=C=>{h.current&&!h.current.contains(C.target)&&L(!1),i.current&&!i.current.contains(C.target)&&S(!1)};return document.addEventListener("mousedown",u),()=>document.removeEventListener("mousedown",u)},[]),e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"AI Provider Configuration"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"✕"})]})}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"relative",ref:h,children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Provider"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:f,onChange:u=>{g(u.target.value),L(!0)},onFocus:()=>L(!0),placeholder:"Search providers...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10"}),e.jsx(H,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary",size:18})]}),w&&e.jsx("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:x.map(u=>e.jsxs("div",{className:"px-4 py-3 cursor-pointer flex items-center space-x-3 hover:bg-adobe-bg-tertiary text-adobe-text-primary",onClick:()=>de(u.value),children:[e.jsx(Ye,{provider:u.value,size:16}),e.jsx("span",{className:"font-medium",children:u.label})]},u.value))})]}),e.jsxs("div",{className:"relative",ref:i,children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Model"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:y,onChange:u=>{j(u.target.value),S(!0)},onFocus:()=>s&&S(!0),placeholder:s?"Search models...":"Select a provider first",disabled:!s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10 disabled:opacity-50"}),e.jsx(H,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary",size:18}),(p==null?void 0:p.isLoading)&&e.jsx(I,{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary animate-spin",size:16})]}),N&&s&&e.jsxs("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:[(p==null?void 0:p.isLoading)&&v.length===0&&e.jsxs("div",{className:"px-4 py-3 text-adobe-text-secondary flex items-center space-x-2",children:[e.jsx(I,{className:"animate-spin",size:16}),e.jsx("span",{children:"Loading models..."})]}),(p==null?void 0:p.error)&&v.length===0&&e.jsx("div",{className:"px-4 py-3 text-adobe-text-secondary",children:p.error}),ie.map(u=>e.jsxs("div",{className:"px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary text-adobe-text-primary",onClick:()=>le(u.id),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:u.name}),u.isRecommended&&e.jsx("span",{className:"text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded",children:"Recommended"})]}),u.description&&e.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1",children:u.description})]},u.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"API Key"}),e.jsx("input",{type:"password",value:m,onChange:u=>b(u.target.value),placeholder:"Enter your API key",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between",children:[e.jsx("button",{onClick:o,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"Cancel"}),e.jsx("button",{onClick:ce,disabled:!s||!d||!m,className:"px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:"Save & Close"})]})]})})},ne=R((o,r)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{o({isLoading:!0,error:null});try{const a=await k("loadHistory()");if(a.success&&a.data){const t=Array.isArray(a.data)?a.data:[];o({sessions:t,isLoading:!1})}else o({sessions:[],isLoading:!1})}catch(a){console.error("Failed to load history:",a),o({error:a.message||"Failed to load chat history",isLoading:!1,sessions:[]})}},saveSession:async a=>{try{o(s=>({sessions:s.sessions.some(n=>n.id===a.id)?s.sessions.map(n=>n.id===a.id?a:n):[...s.sessions,a]}));const t=r().sessions;await k(`saveHistory(${JSON.stringify(t)})`)}catch(t){console.error("Failed to save session:",t),o({error:t.message||"Failed to save session"})}},deleteSession:async a=>{try{o(s=>({sessions:s.sessions.filter(n=>n.id!==a),currentSessionId:s.currentSessionId===a?null:s.currentSessionId}));const t=r().sessions;await k(`saveHistory(${JSON.stringify(t)})`)}catch(t){console.error("Failed to delete session:",t),o({error:t.message||"Failed to delete session"})}},clearHistory:async()=>{try{o({sessions:[],currentSessionId:null}),await k("saveHistory([])")}catch(a){console.error("Failed to clear history:",a),o({error:a.message||"Failed to clear history"})}},createSession:a=>{const t={id:crypto.randomUUID(),title:a||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};return o(s=>({sessions:[t,...s.sessions],currentSessionId:t.id})),t},updateSession:(a,t)=>{o(s=>({sessions:s.sessions.map(n=>n.id===a?{...n,...t,updatedAt:Date.now()}:n)}))},setCurrentSession:a=>{o({currentSessionId:a})},getCurrentSession:()=>{const{sessions:a,currentSessionId:t}=r();return a.find(s=>s.id===t)||null},getSessionById:a=>{const{sessions:t}=r();return t.find(s=>s.id===a)||null},getSortedSessions:()=>{const{sessions:a}=r();return[...a].sort((t,s)=>s.updatedAt-t.updatedAt)}})),Qe=()=>{const{closeModal:o}=A(),{sessions:r}=ne(),[a,t]=c.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[s,n]=c.useState("settings"),[d,l]=c.useState(!1),[m,b]=c.useState(!0),[f,g]=c.useState("30d"),[y,j]=c.useState(!1);c.useEffect(()=>{(async()=>{try{const x=await M.load();x.appSettings&&t(x.appSettings)}catch(x){console.error("Failed to load settings:",x)}finally{b(!1)}})()},[]);const w=[{id:"settings",title:"General Settings",description:"Configure application preferences",icon:e.jsx(q,{size:16,className:"text-adobe-accent"})},{id:"analytics",title:"Analytics",description:"View usage statistics",icon:e.jsx(we,{size:16,className:"text-adobe-accent"})},{id:"help",title:"Help & Support",description:"Get help and answers",icon:e.jsx(Ce,{size:16,className:"text-adobe-accent"})},{id:"about",title:"About",description:"About SahAI Extension",icon:e.jsx(J,{size:16,className:"text-adobe-accent"})}],N=(()=>{const i=Date.now(),x=r.filter(p=>{if(f==="all")return!0;const v=parseInt(f.replace("d",""));return i-p.createdAt<=v*24*60*60*1e3});return{messages:x.reduce((p,v)=>p+v.messages.length,0),sessions:x.length,tokens:x.reduce((p,v)=>p+(v.tokenCount||0),0),cost:x.reduce((p,v)=>p+(v.cost||0),0),avgLatency:x.length>0?x.reduce((p,v)=>p+(v.avgLatency||0),0)/x.length:0}})(),S=()=>{j(!0),setTimeout(()=>j(!1),800)},h=async()=>{l(!0);try{const x={...await M.load(),appSettings:a};await M.save(x),o()}catch(i){console.error("Failed to save settings:",i)}finally{l(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(O,{size:20})})]})}),e.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[e.jsx("div",{className:"w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto",children:e.jsx("div",{className:"space-y-1",children:w.map(i=>e.jsx("button",{onClick:()=>n(i.id),className:`w-full text-left p-3 rounded-md transition-colors ${s===i.id?"bg-adobe-accent/10 text-adobe-text-primary":"text-adobe-text-secondary hover:bg-adobe-bg-tertiary"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 rounded-md bg-adobe-bg-tertiary",children:i.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:i.title}),e.jsx("div",{className:"text-xs mt-1",children:i.description})]})]})},i.id))})}),e.jsx("div",{className:"w-2/3 p-6 overflow-y-auto",children:m?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):s==="settings"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:a.theme,onChange:i=>t(x=>({...x,theme:i.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none",children:[e.jsx("option",{value:"auto",children:"Auto (System)"}),e.jsx("option",{value:"light",children:"Light"}),e.jsx("option",{value:"dark",children:"Dark"})]}),e.jsx(P,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:a.autoSave,onChange:i=>t(x=>({...x,autoSave:i.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:a.showNotifications,onChange:i=>t(x=>({...x,showNotifications:i.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",a.maxHistoryItems,")"]}),e.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:a.maxHistoryItems,onChange:i=>t(x=>({...x,maxHistoryItems:parseInt(i.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:a.debugMode,onChange:i=>t(x=>({...x,debugMode:i.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:h,disabled:d,className:"flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(q,{size:16}),e.jsx("span",{children:d?"Saving...":"Save Settings"})]})})]}):s==="analytics"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Analytics"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:f,onChange:i=>g(i.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-1.5 pr-8 text-sm text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"all",children:"All time"})]}),e.jsx(P,{size:14,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]}),e.jsx("button",{onClick:S,disabled:y,className:"p-1.5 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(ee,{size:16,className:y?"animate-spin":""})})]})]}),y?e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(4)].map((i,x)=>e.jsx("div",{className:"h-24 bg-adobe-bg-secondary rounded-md animate-pulse"},x))}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Messages"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:N.messages.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Sessions"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:N.sessions.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Tokens Used"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:N.tokens.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Est. Cost"}),e.jsxs("div",{className:"text-2xl font-medium text-adobe-text-primary",children:["$",N.cost.toFixed(4)]})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Average Latency"}),e.jsxs("div",{className:"text-sm font-medium text-adobe-text-primary",children:[N.avgLatency.toFixed(2)," seconds"]})]}),e.jsx("div",{className:"h-2 bg-adobe-bg-tertiary rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-adobe-accent transition-all duration-300",style:{width:`${Math.min(100,N.avgLatency*50)}%`}})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary mb-2",children:"Performance Tips"}),e.jsxs("ul",{className:"text-sm text-adobe-text-secondary space-y-1",children:[e.jsx("li",{children:"• Use concise prompts to reduce token usage"}),e.jsx("li",{children:"• Select faster models for simple tasks"}),e.jsx("li",{children:"• Monitor usage to optimize costs"})]})]})]})]}):s==="help"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Help & Support"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Documentation"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Read our comprehensive documentation for detailed guides."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"FAQ"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Find answers to frequently asked questions."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Contact Support"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Email <NAME_EMAIL> for assistance."})]})]})]}):e.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:e.jsxs("div",{className:"text-center",children:[e.jsx(J,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"About SahAI"}),e.jsx("p",{className:"text-sm mb-4",children:"Version 2.0.0"}),e.jsx("p",{className:"text-sm",children:"AI-powered assistant for Adobe Creative Suite"})]})})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-3 text-xs text-adobe-text-secondary text-center",children:[s==="settings"&&"General Settings",s==="analytics"&&"Usage Analytics",s==="help"&&"Help & Support",s==="about"&&"About SahAI"]})]})})},Xe=()=>{const{closeModal:o}=A(),{sessions:r,isLoading:a,error:t,loadHistory:s,deleteSession:n,setCurrentSession:d,getSortedSessions:l}=ne(),[m,b]=c.useState(""),[f,g]=c.useState(null),[y,j]=c.useState("recent");c.useEffect(()=>{s()},[s]);const w=l().filter(h=>h.title.toLowerCase().includes(m.toLowerCase())||h.messages.some(i=>i.content.toLowerCase().includes(m.toLowerCase()))).sort((h,i)=>y==="alphabetical"?h.title.localeCompare(i.title):y==="oldest"?h.createdAt-i.createdAt:i.createdAt-h.createdAt),L=async(h,i)=>{i.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await n(h)},N=h=>{const i=new Date(h),p=(new Date().getTime()-i.getTime())/(1e3*60*60);return p<24?i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):p<24*7?i.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):i.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},S=h=>{const i=h.messages[h.messages.length-1];if(!i)return"No messages";const x=i.content.slice(0,100);return x.length<i.content.length?`${x}...`:x};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),e.jsx("button",{onClick:o,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(O,{size:20})})]})}),e.jsx("div",{className:"p-4 border-b border-adobe-border",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:m,onChange:h=>b(h.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"}),e.jsx("button",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",onClick:()=>b(""),children:m?e.jsx(O,{size:16}):e.jsx(H,{size:16})})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:y,onChange:h=>j(h.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"recent",children:"Most Recent"}),e.jsx("option",{value:"oldest",children:"Oldest First"}),e.jsx("option",{value:"alphabetical",children:"Alphabetical"})]}),e.jsx(P,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:a?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):t?e.jsxs("div",{className:"p-4 text-center text-adobe-error",children:[e.jsx("p",{children:"Error loading history:"}),e.jsx("p",{className:"text-sm",children:t})]}):w.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(Y,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:m?"No matching sessions found":"No chat history yet"}),e.jsx("p",{className:"text-sm",children:m?"Try a different search term":"Start a new conversation to see it here"})]}):e.jsx("div",{className:"h-full overflow-y-auto p-2 space-y-2",children:w.map(h=>e.jsxs("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${(f==null?void 0:f.id)===h.id?"bg-adobe-accent/10 border-l-2 border-adobe-accent":"bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary"}`,onClick:()=>g(h),children:[e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:h.title}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:S(h)})]}),e.jsx("button",{onClick:i=>L(h.id,i),className:"text-adobe-text-secondary hover:text-adobe-error transition-colors p-1",title:"Delete session",children:e.jsx(Le,{size:14})})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(Y,{size:12}),e.jsxs("span",{children:[h.messages.length," messages"]})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(ke,{size:12}),e.jsx("span",{children:N(h.createdAt)})]})]})]},h.id))})}),e.jsxs("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center",children:["Showing ",w.length," of ",r.length," chat sessions"]})]})})},Ze=()=>{const{closeModal:o}=A(),{getActiveProvider:r,providers:a}=z(),[t,s]=c.useState({isOnline:null,isChecking:!1}),n=r(),d=async(f=!1)=>{if(!(n!=null&&n.isConfigured)){s({isOnline:null,isChecking:!1});return}s(g=>({...g,isChecking:!0,error:void 0}));try{const g=await U.checkProviderStatus(n.id,{apiKey:n.apiKey,baseURL:n.baseURL});s({isOnline:g.isOnline,latency:g.latency,isChecking:!1,lastChecked:Date.now()})}catch(g){s({isOnline:!1,isChecking:!1,error:g.message,lastChecked:Date.now()})}};c.useEffect(()=>{d()},[n]);const l=()=>t.isChecking?e.jsx(I,{size:20,className:"animate-spin text-yellow-500"}):t.isOnline===!0?e.jsx(Me,{size:20,className:"text-green-500"}):t.isOnline===!1?e.jsx(Ee,{size:20,className:"text-red-500"}):e.jsx(W,{size:20,className:"text-gray-500"}),m=()=>t.isChecking?"Checking connection...":t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown",b=()=>t.isChecking?"text-yellow-600":t.isOnline===!0?"text-green-600":t.isOnline===!1?"text-red-600":"text-gray-600";return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-adobe-bg-secondary border border-adobe-border rounded-lg shadow-xl w-full max-w-md mx-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-adobe-border",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Provider Status"}),e.jsx("button",{onClick:o,className:"p-1 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded transition-colors",children:e.jsx(O,{size:20})})]}),e.jsx("div",{className:"p-4 space-y-4",children:n?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-adobe-bg-tertiary rounded-lg",children:[e.jsx("div",{className:"flex-shrink-0",children:l()}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary",children:n.name}),e.jsx("p",{className:`text-sm font-medium ${b()}`,children:m()})]}),e.jsx("button",{onClick:()=>d(!0),disabled:t.isChecking,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-secondary rounded transition-colors disabled:opacity-50",title:"Refresh status",children:e.jsx(ee,{size:16,className:t.isChecking?"animate-spin":""})})]}),e.jsxs("div",{className:"space-y-3",children:[t.latency&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-adobe-text-secondary",children:"Latency:"}),e.jsxs("span",{className:"text-adobe-text-primary font-medium",children:[t.latency,"ms"]})]}),t.lastChecked&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-adobe-text-secondary",children:"Last checked:"}),e.jsx("span",{className:"text-adobe-text-primary font-medium",children:new Date(t.lastChecked).toLocaleTimeString()})]}),n.baseURL&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-adobe-text-secondary",children:"Endpoint:"}),e.jsx("span",{className:"text-adobe-text-primary font-medium truncate ml-2",children:n.baseURL})]}),t.error&&e.jsxs("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:[e.jsx("p",{className:"text-sm text-red-800 font-medium",children:"Error:"}),e.jsx("p",{className:"text-sm text-red-700 mt-1",children:t.error})]})]})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(W,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-adobe-text-secondary",children:"No provider selected"}),e.jsx("p",{className:"text-sm text-adobe-text-tertiary mt-2",children:"Select a provider to check connection status"})]})}),e.jsx("div",{className:"p-4 border-t border-adobe-border bg-adobe-bg-tertiary rounded-b-lg",children:e.jsx("p",{className:"text-xs text-adobe-text-tertiary text-center",children:"Status is automatically checked every 30 seconds"})})]})})},et=()=>{const{modal:o}=A();if(!o)return null;switch(o){case"provider":return e.jsx(We,{});case"settings":return e.jsx(Qe,{});case"chat-history":return e.jsx(Xe,{});case"status":return e.jsx(Ze,{});default:return null}},tt=()=>e.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[e.jsx(_e,{}),e.jsx(qe,{}),e.jsx(Je,{}),e.jsx(et,{})]});ae();z.getState().loadSettings();B.createRoot(document.getElementById("root")).render(e.jsx(X.StrictMode,{children:e.jsx(tt,{})}));
