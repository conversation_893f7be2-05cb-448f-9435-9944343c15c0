import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  base: './',                              // relative paths for CEP
  root: 'client',                          // source folder
  build: {
    outDir: resolve(__dirname, 'dist'),    // 1. build into /dist
    emptyOutDir: true,                     // 2. clean before every build
    // Optimize for Shi<PERSON>: never inline WASM or large JSON files into JS bundles
    assetsInlineLimit: 0,
    // Raise the warning threshold to monitor chunk sizes after optimization
    chunkSizeWarningLimit: 600,
    rollupOptions: {
      output: {
        entryFileNames: 'index.js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        // Manual chunking for better optimization
        manualChunks: {
          // Separate vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['lucide-react', 'zustand'],
          // Keep shiki separate for better caching
          'shiki': ['shiki']
        }
      },
      // Exclude unwanted Shiki language files from bundling
      external: (id) => {
        // Allow our essential languages
        const essentialLangs = [
          'javascript', 'typescript', 'jsx', 'tsx',
          'html', 'css', 'scss', 'less',
          'json', 'jsonc', 'xml', 'yaml',
          'markdown',
          'python', 'swift', 'rust', 'go', 'java', 'php', 'ruby', 'shell',
          'actionscript-3'
        ];

        // Check if this is a Shiki language file
        if (id.includes('@shikijs/langs/dist/') && id.endsWith('.mjs')) {
          const langName = id.split('/').pop()?.replace('.mjs', '');
          // Only bundle essential languages, exclude all others
          return !essentialLangs.includes(langName || '');
        }

        return false; // Don't exclude anything else
      }
    },
  },
  server: { port: 3000, strictPort: true },
});